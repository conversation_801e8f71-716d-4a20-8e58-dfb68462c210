package com.ylzx.file.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ylzx.file.domain.FileInfo;
import com.ylzx.file.domain.dto.FileUploadRequest;
import com.ylzx.file.domain.dto.FileUploadResponse;
import com.ylzx.file.mapper.FileInfoMapper;
import com.ylzx.file.service.BatchFileService;
import com.ylzx.file.service.FileService;
import com.ylzx.file.util.MinioUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 批量文件处理服务实现类
 * 使用虚线程优化批量操作性能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BatchFileServiceImpl implements BatchFileService {

    private final FileService fileService;
    private final MinioUtil minioUtil;
    private final FileInfoMapper fileInfoMapper;

    @Override
    public CompletableFuture<List<FileUploadResponse>> uploadFilesAsync(List<MultipartFile> files, FileUploadRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            List<CompletableFuture<FileUploadResponse>> futures = files.stream()
                    .map(file -> CompletableFuture.supplyAsync(() -> {
                        try {
                            return fileService.uploadFile(file, request);
                        } catch (Exception e) {
                            log.error("异步上传文件失败: {}", file.getOriginalFilename(), e);
                            return null;
                        }
                    }))
                    .toList();

            return futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        });
    }

    @Override
    public Map<String, String> getPresignedUrls(List<String> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, String> result = new ConcurrentHashMap<>();
        
        // 使用虚线程并行处理
        List<CompletableFuture<Void>> futures = fileIds.stream()
                .map(fileId -> CompletableFuture.runAsync(() -> {
                    try {
                        String url = fileService.getPresignedUrl(fileId);
                        if (StrUtil.isNotBlank(url)) {
                            result.put(fileId, url);
                        }
                    } catch (Exception e) {
                        log.error("获取预签名URL失败: {}", fileId, e);
                    }
                }))
                .toList();

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        return result;
    }

    @Override
    public CompletableFuture<Map<String, String>> getPresignedUrlsAsync(List<String> fileIds) {
        return CompletableFuture.supplyAsync(() -> getPresignedUrls(fileIds));
    }

    @Override
    public Map<String, InputStream> downloadFiles(List<String> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, InputStream> result = new ConcurrentHashMap<>();
        
        // 使用虚线程并行下载
        List<CompletableFuture<Void>> futures = fileIds.stream()
                .map(fileId -> CompletableFuture.runAsync(() -> {
                    try {
                        InputStream inputStream = fileService.downloadFile(fileId);
                        if (inputStream != null) {
                            result.put(fileId, inputStream);
                        }
                    } catch (Exception e) {
                        log.error("下载文件失败: {}", fileId, e);
                    }
                }))
                .toList();

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        return result;
    }

    @Override
    public CompletableFuture<Map<String, InputStream>> downloadFilesAsync(List<String> fileIds) {
        return CompletableFuture.supplyAsync(() -> downloadFiles(fileIds));
    }

    @Override
    public Map<String, FileInfo> checkFilesExist(List<String> md5Hashes) {
        if (md5Hashes == null || md5Hashes.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, FileInfo> result = new ConcurrentHashMap<>();
        
        // 使用虚线程并行检查
        List<CompletableFuture<Void>> futures = md5Hashes.stream()
                .map(md5Hash -> CompletableFuture.runAsync(() -> {
                    try {
                        FileInfo fileInfo = fileService.checkFileExists(md5Hash);
                        if (fileInfo != null) {
                            result.put(md5Hash, fileInfo);
                        }
                    } catch (Exception e) {
                        log.error("检查文件是否存在失败: {}", md5Hash, e);
                    }
                }))
                .toList();

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        return result;
    }

    @Override
    public CompletableFuture<Map<String, FileInfo>> checkFilesExistAsync(List<String> md5Hashes) {
        return CompletableFuture.supplyAsync(() -> checkFilesExist(md5Hashes));
    }

    @Override
    public List<FileUploadResponse> extractAndUploadFromArchive(MultipartFile archiveFile, String folderPath, String createBy) {
        // 这里需要实现压缩文件解压和批量上传的逻辑
        // 由于实现较复杂，暂时返回空列表，后续可以扩展
        log.warn("压缩文件解压上传功能暂未实现");
        return new ArrayList<>();
    }

    @Override
    public CompletableFuture<List<FileUploadResponse>> extractAndUploadFromArchiveAsync(MultipartFile archiveFile, String folderPath, String createBy) {
        return CompletableFuture.supplyAsync(() -> extractAndUploadFromArchive(archiveFile, folderPath, createBy));
    }

    @Override
    public List<FileInfo> getFileInfos(List<String> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 批量查询数据库
        return fileInfoMapper.selectBatchIds(fileIds);
    }

    @Override
    public CompletableFuture<List<FileInfo>> getFileInfosAsync(List<String> fileIds) {
        return CompletableFuture.supplyAsync(() -> getFileInfos(fileIds));
    }
}
