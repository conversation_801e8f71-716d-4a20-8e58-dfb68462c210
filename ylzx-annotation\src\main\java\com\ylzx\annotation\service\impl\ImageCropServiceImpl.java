package com.ylzx.annotation.service.impl;

import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.ylzx.annotation.domain.AnnotationImages;
import com.ylzx.annotation.jni.DatasetExportNative;
import com.ylzx.annotation.service.ImageCropService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 图像裁剪服务实现类
 * 统一使用C++进行图像处理，移除Java图像处理逻辑
 */
@Slf4j
@Service
public class ImageCropServiceImpl implements ImageCropService {

    @Autowired
    private DatasetExportNative exportNative;

    @Override
    public CropResult cropImageByAnnotations(AnnotationImages image, List<AnnotationAnnotations> annotations,
                                           String outputPath, CropConfig cropConfig) {
        try {
            // 构建标注坐标字符串
            StringBuilder coordinatesBuilder = new StringBuilder();
            for (int i = 0; i < annotations.size(); i++) {
                if (i > 0) {
                    coordinatesBuilder.append(";");
                }
                coordinatesBuilder.append(annotations.get(i).getCoordinates());
            }

            // 构建图像路径
            // TODO: 需要实现基于MinIO的图片路径获取
            String imagePath = image.getFileId(); // 临时使用fileId

            // 直接调用C++进行智能裁剪
            boolean success = exportNative.smartCropByAnnotations(
                imagePath,
                outputPath,
                coordinatesBuilder.toString(),
                convertToCppConfig(cropConfig)
            );

            if (success) {
                return new CropResult(true, "裁剪成功", outputPath);
            } else {
                return new CropResult(false, "C++裁剪失败", null);
            }

        } catch (Exception e) {
            log.error("图像裁剪失败: {}", image.getImageId(), e);
            return new CropResult(false, "裁剪异常: " + e.getMessage(), null);
        }
    }

    @Override
    public BatchCropResult batchCropImages(List<ImageAnnotationPair> imageAnnotationPairs,
                                         String outputBasePath, CropConfig cropConfig) {
        long startTime = System.currentTimeMillis();
        BatchCropResult batchResult = new BatchCropResult();

        try {
            // 准备批量处理的数据
            List<String> imagePaths = new ArrayList<>();
            List<String> outputPaths = new ArrayList<>();
            List<String> annotationCoordinatesArray = new ArrayList<>();

            for (int i = 0; i < imageAnnotationPairs.size(); i++) {
                ImageAnnotationPair pair = imageAnnotationPairs.get(i);
                AnnotationImages image = pair.getImage();
                List<AnnotationAnnotations> annotations = pair.getAnnotations();

                // 图像路径
                // TODO: 需要实现基于MinIO的图片路径获取
                String imagePath = image.getFileId(); // 临时使用fileId
                imagePaths.add(imagePath);

                // 输出路径
                String outputFileName = generateOutputFileName(image, i);
                String outputPath = Paths.get(outputBasePath, outputFileName).toString();
                outputPaths.add(outputPath);

                // 标注坐标
                StringBuilder coordinatesBuilder = new StringBuilder();
                for (int j = 0; j < annotations.size(); j++) {
                    if (j > 0) {
                        coordinatesBuilder.append(";");
                    }
                    coordinatesBuilder.append(annotations.get(j).getCoordinates());
                }
                annotationCoordinatesArray.add(coordinatesBuilder.toString());
            }

            // 调用C++批量智能裁剪
            DatasetExportNative.BatchCropResult nativeResult = exportNative.batchSmartCropByAnnotations(
                imagePaths.toArray(new String[0]),
                outputPaths.toArray(new String[0]),
                annotationCoordinatesArray.toArray(new String[0]),
                convertToCppConfig(cropConfig),
                Math.min(8, imageAnnotationPairs.size())
            );

            // 转换结果
            batchResult.setSuccess(nativeResult.success);
            batchResult.setMessage(nativeResult.message);
            batchResult.setTotalCount(nativeResult.totalCount);
            batchResult.setSuccessCount(nativeResult.successCount);
            batchResult.setFailedCount(nativeResult.failedCount);

            List<CropResult> results = new ArrayList<>();
            for (int i = 0; i < imageAnnotationPairs.size(); i++) {
                CropResult result = new CropResult();
                if (i < nativeResult.successCount) {
                    result.setSuccess(true);
                    result.setMessage("裁剪成功");
                    if (nativeResult.outputPaths != null && i < nativeResult.outputPaths.length) {
                        result.setOutputPath(nativeResult.outputPaths[i]);
                    }
                } else {
                    result.setSuccess(false);
                    result.setMessage("裁剪失败");
                }
                results.add(result);
            }
            batchResult.setResults(results);

        } catch (Exception e) {
            log.error("批量图像裁剪失败", e);
            batchResult.setSuccess(false);
            batchResult.setMessage("批量裁剪异常: " + e.getMessage());
            batchResult.setTotalCount(imageAnnotationPairs.size());
            batchResult.setSuccessCount(0);
            batchResult.setFailedCount(imageAnnotationPairs.size());
        } finally {
            batchResult.setTotalProcessingTimeMs(System.currentTimeMillis() - startTime);
        }

        return batchResult;
    }

    /**
     * 转换为C++配置对象
     */
    private DatasetExportNative.CropConfig convertToCppConfig(CropConfig cropConfig) {
        DatasetExportNative.CropConfig cppConfig = new DatasetExportNative.CropConfig();
        cppConfig.targetWidth = cropConfig.getTargetWidth() != null ? cropConfig.getTargetWidth() : 0;
        cppConfig.targetHeight = cropConfig.getTargetHeight() != null ? cropConfig.getTargetHeight() : 0;
        cppConfig.padding = cropConfig.getPadding();
        cppConfig.enableRandomPlacement = cropConfig.isEnableRandomPlacement();
        cppConfig.backgroundColor = cropConfig.getBackgroundColor();
        cppConfig.maintainAspectRatio = cropConfig.isMaintainAspectRatio();
        return cppConfig;
    }

    /**
     * 生成输出文件名
     */
    private String generateOutputFileName(AnnotationImages image, int index) {
        String originalName = image.getOriginalFilename();
        String baseName = originalName.substring(0, originalName.lastIndexOf('.'));
        String extension = originalName.substring(originalName.lastIndexOf('.'));
        return String.format("%s_crop_%d_%d%s", baseName, image.getImageId(), index, extension);
    }
}