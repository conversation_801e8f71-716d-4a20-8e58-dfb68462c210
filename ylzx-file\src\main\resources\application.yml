# 服务器配置
server:
  port: 8083
  servlet:
    context-path: /file

# Spring配置
spring:
  application:
    name: ylzx-file
  profiles:
    active: dev
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  threads:
    virtual:
      enabled: true  # 启用虚拟线程
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ************************************************
    username: postgres
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.ylzx.file.domain
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# MinIO配置
minio:
  endpoint: http://localhost:9000
  access-key: minioadmin
  secret-key: minioadmin
  bucket-name: data-annotation
  secure: false

# 日志配置
logging:
  level:
    com.ylzx.file: debug
    org.springframework.web: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# SpringDoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: YLZX文件服务API
    description: 基于MinIO的文件存储服务
    version: 1.0.0
