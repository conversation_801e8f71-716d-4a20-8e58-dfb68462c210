-- 优化的文件信息表
-- 去除了owner_id、platform、is_static、sort等不必要字段
-- 专注于文件存储的核心功能
CREATE TABLE file_info (
    id VARCHAR(32) NOT NULL COMMENT '文件ID，使用UUID',
    file_name VARCHAR(500) NOT NULL COMMENT '文件名（存储在MinIO中的文件名）',
    original_filename VARCHAR(500) NOT NULL COMMENT '原始文件名',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    content_type VARCHAR(100) COMMENT '文件MIME类型',
    file_extension VARCHAR(20) COMMENT '文件扩展名',
    file_path VARCHAR(1000) NOT NULL COMMENT 'MinIO中的文件路径（包含文件夹）',
    file_url VARCHAR(1000) COMMENT '文件访问URL',
    md5_hash VARCHAR(32) COMMENT '文件MD5哈希值，用于去重',
    
    -- 缩略图相关（可选）
    thumbnail_path VARCHAR(1000) COMMENT '缩略图路径',
    thumbnail_url VARCHAR(1000) COMMENT '缩略图访问URL',
    thumbnail_size BIGINT COMMENT '缩略图大小（字节）',
    
    -- 文件状态
    status SMALLINT DEFAULT 1 COMMENT '文件状态：1-正常，0-已删除',
    
    -- 审计字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    remark TEXT COMMENT '备注',
    
    PRIMARY KEY (id)
);

-- 创建索引
CREATE INDEX idx_file_info_md5 ON file_info(md5_hash);
CREATE INDEX idx_file_info_path ON file_info(file_path);
CREATE INDEX idx_file_info_extension ON file_info(file_extension);
CREATE INDEX idx_file_info_create_time ON file_info(create_time);
CREATE INDEX idx_file_info_status ON file_info(status);

-- 添加表注释
COMMENT ON TABLE file_info IS '文件信息表 - 基于MinIO的文件存储';

-- 字段注释
COMMENT ON COLUMN file_info.id IS '文件唯一标识，使用UUID';
COMMENT ON COLUMN file_info.file_name IS '存储在MinIO中的文件名，通常包含时间戳等唯一标识';
COMMENT ON COLUMN file_info.original_filename IS '用户上传时的原始文件名';
COMMENT ON COLUMN file_info.file_path IS 'MinIO中的完整路径，如：images/2024/01/01/filename.jpg';
COMMENT ON COLUMN file_info.file_url IS '文件的访问URL，可以是MinIO的预签名URL或代理URL';
COMMENT ON COLUMN file_info.md5_hash IS '文件内容的MD5哈希值，用于文件去重和完整性校验';
COMMENT ON COLUMN file_info.status IS '文件状态：1-正常可用，0-已删除（软删除）';
