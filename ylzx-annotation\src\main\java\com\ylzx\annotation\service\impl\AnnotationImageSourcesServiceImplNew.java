package com.ylzx.annotation.service.impl;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylzx.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.ylzx.annotation.mapper.AnnotationImageSourcesMapper;
import com.ylzx.annotation.domain.AnnotationImageSources;
import com.ylzx.annotation.service.AnnotationImageSourcesService;
import com.ylzx.annotation.service.AnnotationImagesService;
import com.ylzx.annotation.service.AnnotationCategoriesSourcesService;
import com.ylzx.common.utils.ArchiveUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.core.io.ByteArrayResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * 标注图片来源Service业务层处理（新版本 - 使用MinIO）
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
public class AnnotationImageSourcesServiceImplNew extends ServiceImpl<AnnotationImageSourcesMapper, AnnotationImageSources> implements AnnotationImageSourcesService {

    @Autowired
    private AnnotationImageSourcesMapper annotationImageSourcesMapper;

    @Autowired
    private AnnotationImagesService annotationImagesService;

    @Autowired
    private AnnotationCategoriesSourcesService categoriesSourcesService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    // ylzx-file服务的基础URL
    private static final String FILE_SERVICE_URL = "http://localhost:8083/file/api/file";

    /**
     * 上传文件到MinIO
     */
    private String uploadFileToMinio(MultipartFile file, String folderPath, String createBy) {
        try {
            // 准备请求参数
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", new ByteArrayResource(file.getBytes()) {
                @Override
                public String getFilename() {
                    return file.getOriginalFilename();
                }
            });
            body.add("folderPath", folderPath);
            body.add("createBy", createBy);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.postForEntity(
                FILE_SERVICE_URL + "/upload", requestEntity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                if (jsonNode.get("code").asInt() == 200) {
                    return jsonNode.get("data").get("id").asText();
                } else {
                    throw new RuntimeException("文件上传失败: " + jsonNode.get("msg").asText());
                }
            } else {
                throw new RuntimeException("文件上传失败，HTTP状态码: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("上传文件到MinIO失败", e);
            throw new RuntimeException("上传文件到MinIO失败: " + e.getMessage());
        }
    }

    /**
     * 从MinIO获取预签名URL
     */
    private String getPresignedUrl(String fileId) {
        try {
            ResponseEntity<String> response = restTemplate.getForEntity(
                FILE_SERVICE_URL + "/presigned-url/" + fileId, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                if (jsonNode.get("code").asInt() == 200) {
                    return jsonNode.get("data").asText();
                }
            }
            return null;
        } catch (Exception e) {
            log.error("获取预签名URL失败: {}", fileId, e);
            return null;
        }
    }

    /**
     * 批量获取预签名URL
     */
    public Map<String, String> getPresignedUrls(List<String> fileIds) {
        Map<String, String> result = new HashMap<>();
        for (String fileId : fileIds) {
            String url = getPresignedUrl(fileId);
            if (url != null) {
                result.put(fileId, url);
            }
        }
        return result;
    }

    /**
     * 从MinIO下载文件
     */
    private InputStream downloadFileFromMinio(String fileId) {
        try {
            ResponseEntity<byte[]> response = restTemplate.getForEntity(
                FILE_SERVICE_URL + "/download/" + fileId, byte[].class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return new java.io.ByteArrayInputStream(response.getBody());
            }
            return null;
        } catch (Exception e) {
            log.error("从MinIO下载文件失败: {}", fileId, e);
            return null;
        }
    }

    /**
     * 计算文件的部分哈希值
     */
    private String calculatePartialHash(MultipartFile file) throws IOException, NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        
        try (InputStream inputStream = file.getInputStream()) {
            byte[] buffer = new byte[8192];
            int bytesRead = inputStream.read(buffer);
            if (bytesRead > 0) {
                md.update(buffer, 0, bytesRead);
            }
        }
        
        byte[] digest = md.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    /**
     * 检查重复哈希
     */
    private void checkDuplicateHash(String hash) {
        AnnotationImageSources query = new AnnotationImageSources();
        query.setContentHash(hash);
        List<AnnotationImageSources> existing = annotationImageSourcesMapper.selectAnnotationImageSourcesList(query);
        if (!existing.isEmpty()) {
            throw new RuntimeException("文件已存在，请勿重复上传");
        }
    }

    /**
     * 保存数据源记录
     */
    private AnnotationImageSources saveSourceRecord(String fileId, String originalFilename, String uploadType, String hash) {
        AnnotationImageSources sources = new AnnotationImageSources();
        sources.setFileId(fileId);  // 使用fileId替代path
        sources.setArchiveFileId(fileId);  // 使用archiveFileId替代archivePath
        sources.setContentHash(hash);
        sources.setUploadType(uploadType);
        sources.setCreateBy(SecurityUtils.getUsername());
        sources.setUpdateBy(SecurityUtils.getUsername());
        
        annotationImageSourcesMapper.insertAnnotationImageSources(sources);
        return sources;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnotationImageSources uploadSource(MultipartFile file) throws IOException {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new IllegalArgumentException("上传文件名不能为空");
        }

        try {
            // 1. 计算文件哈希并检查重复
            String hash = calculatePartialHash(file);
            checkDuplicateHash(hash);

            // 2. 上传文件到MinIO
            String fileId = uploadFileToMinio(file, "annotation/archives", SecurityUtils.getUsername());
            
            // 3. 保存数据源记录
            AnnotationImageSources sources = saveSourceRecord(fileId, originalFilename, "manual_upload", hash);

            // 4. 处理图片
            List<AnnotationImageSources> annotationImageSources = annotationImageSourcesMapper.selectAnnotationImageSourcesList(sources);
            if (annotationImageSources.size() == 1){
                annotationImagesService.processImagesFromSourceId(annotationImageSources.get(0).getSourceId(), null);
            }

            return sources;

        } catch (Exception e) {
            log.error("上传数据源失败", e);
            throw new IOException("上传数据源失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnotationImageSources uploadSourceWithCategory(MultipartFile file, Long categoryId) throws IOException {
        // 先上传文件
        AnnotationImageSources sources = uploadSource(file);
        
        // 然后关联分类
        if (categoryId != null) {
            categoriesSourcesService.associateCategoryWithSource(categoryId, sources.getSourceId(), SecurityUtils.getUserId());
        }
        
        return sources;
    }

    @Override
    public AnnotationImageSources selectAnnotationImageSourcesBySourceId(Long sourceId) {
        return annotationImageSourcesMapper.selectAnnotationImageSourcesBySourceId(sourceId);
    }

    @Override
    public List<AnnotationImageSources> selectAnnotationImageSourcesList(AnnotationImageSources annotationImageSources) {
        return annotationImageSourcesMapper.selectAnnotationImageSourcesList(annotationImageSources);
    }

    public int insertAnnotationImageSources(AnnotationImageSources annotationImageSources) {
        return annotationImageSourcesMapper.insertAnnotationImageSources(annotationImageSources);
    }

    public int updateAnnotationImageSources(AnnotationImageSources annotationImageSources) {
        return annotationImageSourcesMapper.updateAnnotationImageSources(annotationImageSources);
    }

    @Override
    public int deleteAnnotationImageSourcesBySourceIds(Long[] sourceIds) {
        return annotationImageSourcesMapper.deleteAnnotationImageSourcesBySourceIds(sourceIds);
    }

    @Override
    public int deleteAnnotationImageSourcesBySourceId(Long sourceId) {
        return annotationImageSourcesMapper.deleteAnnotationImageSourcesBySourceId(sourceId);
    }

    @Override
    public void scanAndImportSources() throws IOException {
        // TODO: 实现基于MinIO的扫描导入功能
        log.warn("scanAndImportSources方法暂未实现MinIO版本");
        throw new UnsupportedOperationException("该方法需要实现MinIO版本");
    }

    @Override
    public void scanAndImportSourcesWithImageProcessing(Long categoryId) throws IOException {
        // TODO: 实现基于MinIO的扫描导入功能
        log.warn("scanAndImportSourcesWithImageProcessing方法暂未实现MinIO版本");
        throw new UnsupportedOperationException("该方法需要实现MinIO版本");
    }

    @Override
    public List<Map<String, Object>> getScanDirectoryFiles() {
        // TODO: 实现基于MinIO的文件列表功能
        log.warn("getScanDirectoryFiles方法暂未实现MinIO版本");
        return new ArrayList<>();
    }

    @Override
    public AnnotationImageSources processScanFile(String filename, Long categoryId) throws IOException {
        // TODO: 实现基于MinIO的文件处理功能
        log.warn("processScanFile方法暂未实现MinIO版本");
        throw new UnsupportedOperationException("该方法需要实现MinIO版本");
    }
}
