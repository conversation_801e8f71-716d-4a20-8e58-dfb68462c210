package com.ylzx.file.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 文件操作异常
     */
    @ExceptionHandler(FileException.class)
    public ResponseEntity<Map<String, Object>> handleFileException(FileException e) {
        log.error("文件操作异常", e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", e.getMessage());
        result.put("code", "FILE_ERROR");
        
        return ResponseEntity.badRequest().body(result);
    }

    /**
     * 文件大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<Map<String, Object>> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.error("文件大小超限", e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "文件大小超过限制");
        result.put("code", "FILE_SIZE_EXCEEDED");
        
        return ResponseEntity.badRequest().body(result);
    }

    /**
     * 参数验证异常
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    public ResponseEntity<Map<String, Object>> handleValidationException(Exception e) {
        log.error("参数验证异常", e);
        
        StringBuilder message = new StringBuilder("参数验证失败：");
        
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException ex = (MethodArgumentNotValidException) e;
            for (FieldError error : ex.getBindingResult().getFieldErrors()) {
                message.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
            }
        } else if (e instanceof BindException) {
            BindException ex = (BindException) e;
            for (FieldError error : ex.getBindingResult().getFieldErrors()) {
                message.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message.toString());
        result.put("code", "VALIDATION_ERROR");
        
        return ResponseEntity.badRequest().body(result);
    }

    /**
     * 通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleException(Exception e) {
        log.error("系统异常", e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "系统内部错误");
        result.put("code", "SYSTEM_ERROR");
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }

    /**
     * 运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<Map<String, Object>> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常", e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", e.getMessage());
        result.put("code", "RUNTIME_ERROR");
        
        return ResponseEntity.badRequest().body(result);
    }
}
