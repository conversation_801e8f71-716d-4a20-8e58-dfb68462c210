package com.ylzx.annotation.service.impl;

import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.file.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylzx.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileSystemUtils;
import org.springframework.web.multipart.MultipartFile;

import com.ylzx.annotation.mapper.AnnotationImageSourcesMapper;
import com.ylzx.annotation.domain.AnnotationImageSources;
import com.ylzx.annotation.service.AnnotationImageSourcesService;
import com.ylzx.annotation.service.AnnotationImagesService;
import com.ylzx.annotation.service.AnnotationCategoriesSourcesService;
import com.ylzx.common.utils.ArchiveUtils;
import com.ylzx.annotation.config.AnnotationPathProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 标注图片来源Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Slf4j
@Service
public class AnnotationImageSourcesServiceImpl extends ServiceImpl<AnnotationImageSourcesMapper,AnnotationImageSources> implements AnnotationImageSourcesService
{
    // 支持的压缩文件格式
    private static final Set<String> SUPPORTED_ARCHIVE_EXTENSIONS = Set.of(".zip", ".tar.gz");

    @Autowired
    private AnnotationImageSourcesMapper annotationImageSourcesMapper;

    @Autowired
    private AnnotationPathProperties pathProperties;

    @Autowired
    private AnnotationImagesService annotationImagesService;

    @Autowired
    private AnnotationCategoriesSourcesService categoriesSourcesService;

    /**
     * 查询标注图片来源
     * 
     * @param sourceId 标注图片来源主键
     * @return 标注图片来源
     */
    @Override
    public AnnotationImageSources selectAnnotationImageSourcesBySourceId(Long sourceId)
    {
        return this.getById(sourceId);
    }

    /**
     * 查询标注图片来源列表
     * 
     * @param annotationImageSources 标注图片来源
     * @return 标注图片来源集合
     */
    @Override
    public List<AnnotationImageSources> selectAnnotationImageSourcesList(AnnotationImageSources annotationImageSources)
    {
        return annotationImageSourcesMapper.selectAnnotationImageSourcesList(annotationImageSources);
    }

    /**
     * 批量删除标注图片来源
     * 
     * @param sourceIds 需要删除的标注图片来源主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationImageSourcesBySourceIds(Long[] sourceIds)
    {
        return annotationImageSourcesMapper.deleteBatchIds(Arrays.asList(sourceIds));
    }

    /**
     * 删除标注图片来源信息
     * 
     * @param sourceId 标注图片来源主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationImageSourcesBySourceId(Long sourceId)
    {
        return annotationImageSourcesMapper.deleteAnnotationImageSourcesBySourceId(sourceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnotationImageSources uploadSource(MultipartFile file) throws IOException {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new IllegalArgumentException("上传文件名不能为空");
        }

        // 1. 先将上传文件存到临时目录
        Path tempFile = null;
        try {
            tempFile = Files.createTempFile(pathProperties.getUploadPath(), "upload_", ".tmp");
            file.transferTo(tempFile);

            // 2. 从临时文件中计算部分哈希并检查重复
            String hash = calculatePartialHash(tempFile);
            checkDuplicateHash(hash);

            // 3. 哈希不重复，将临时文件移动到最终归档目录
            String uniqueFilename = System.currentTimeMillis() + "_" + originalFilename;
            Path archivePath = pathProperties.getUploadPath().resolve(uniqueFilename);
            Files.move(tempFile, archivePath, StandardCopyOption.REPLACE_EXISTING);

            // 4. 解压并保存记录
            AnnotationImageSources sources = unzipAndSaveSourceRecord(archivePath, originalFilename, "manual_upload", hash);

            List<AnnotationImageSources> annotationImageSources = annotationImageSourcesMapper.selectAnnotationImageSourcesList(sources);
            if (annotationImageSources.size() == 1){
                annotationImagesService.processImagesFromSourceId(annotationImageSources.get(0).getSourceId(), null);
            }





            return sources;

        } catch (Exception e) {
            // 如果后续处理失败，删除已保存的压缩文件和临时文件
            if (tempFile != null) {
                Files.deleteIfExists(tempFile);
            }
            throw new IOException(e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnotationImageSources uploadSourceWithCategory(MultipartFile file, Long categoryId) throws IOException {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new IllegalArgumentException("上传文件名不能为空");
        }

        // 1. 先将上传文件存到临时目录
        Path tempFile = null;
        try {
            tempFile = Files.createTempFile(pathProperties.getUploadPath(), "upload_", ".tmp");
            file.transferTo(tempFile);

            // 2. 从临时文件中计算部分哈希并检查重复
            String hash = calculatePartialHash(tempFile);
            checkDuplicateHash(hash);

            // 3. 哈希不重复，将临时文件移动到最终归档目录
            String uniqueFilename = System.currentTimeMillis() + "_" + originalFilename;
            Path archivePath = pathProperties.getUploadPath().resolve(uniqueFilename);
            Files.move(tempFile, archivePath, StandardCopyOption.REPLACE_EXISTING);

            // 4. 解压并保存记录
            AnnotationImageSources source = unzipAndSaveSourceRecord(archivePath, originalFilename, "manual_upload", hash);

            // 5. 如果指定了分类ID，创建分类与数据源的关联
            if (categoryId != null) {
                categoriesSourcesService.associateCategoryWithSource(categoryId, source.getSourceId(), null);
                log.info("已创建分类{}与数据源{}的关联", categoryId, source.getSourceId());

                // 6. 自动处理图片并关联到指定分类
                try {
                    annotationImagesService.processImagesFromSourceId(source.getSourceId(), categoryId);
                    log.info("已自动处理数据源{}的图片并关联到分类{}", source.getSourceId(), categoryId);
                } catch (Exception e) {
                    log.error("自动处理图片失败，数据源ID: {}, 分类ID: {} - 异常: {}", source.getSourceId(), categoryId, e.getMessage(), e);
                    // 不抛出异常，允许手动后续处理
                }
            }

            return source;
        } catch (Exception e) {
            // 如果后续处理失败，删除已保存的压缩文件和临时文件
            if (tempFile != null) {
                Files.deleteIfExists(tempFile);
            }
            throw new IOException("上传和处理文件失败", e);
        }
    }

    @Override
    public void scanAndImportSources() throws IOException {
        int processedCount = 0;
        int skippedCount = 0;
        int errorCount = 0;

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(pathProperties.getScanPath())) {
            for (Path filePath : stream) {
                if (Files.isRegularFile(filePath)) {
                    String filename = filePath.getFileName().toString();
                    if (isSupportedArchiveFile(filename)) {
                        String hash = null;
                        try {
                            log.info("正在处理文件: {}", filename);
                            // 1. 计算部分哈希并检查重复
                            hash = calculatePartialHash(filePath);
                            checkDuplicateHash(hash);

                            // 2. 解压并保存记录
                            AnnotationImageSources source = unzipAndSaveSourceRecord(filePath, filename, "scanned_import", hash);

                            // 2.1 自动处理解压后的图片（可选，根据需要启用）
                            // processImagesAfterExtraction(source);

                            // 3. 移动已处理的文件
                            Path destination = pathProperties.getProcessedPath().resolve(filename);
                            Files.move(filePath, destination, StandardCopyOption.REPLACE_EXISTING);
                            log.info("成功处理并移动文件: {}", filename);
                            processedCount++;
                        } catch (IllegalStateException e) {
                            log.warn("跳过文件（内容重复）: {} - 异常: {}", filename, e.getMessage());
                            // 对于重复文件，也移动到已处理目录，防止重复扫描
                            Path destination = pathProperties.getProcessedPath().resolve(filename);
                            Files.move(filePath, destination, StandardCopyOption.REPLACE_EXISTING);
                            skippedCount++;
                        } catch (Exception e) {
                            log.error("处理文件失败: {} - 异常: {}", filename, e.getMessage(), e);
                            // 移动到错误目录
                            try {
                                Path errorDestination = pathProperties.getErrorPath().resolve(filename);
                                Files.move(filePath, errorDestination, StandardCopyOption.REPLACE_EXISTING);
                                log.warn("已将失败文件移动到错误目录: {}", filename);
                            } catch (IOException moveException) {
                                log.error("移动失败文件到错误目录时出错: {} - 异常: {}", filename, moveException.getMessage(), moveException);
                            }
                            errorCount++;
                        }
                    }
                }
            }
        }

        log.info("扫描任务完成 - 成功处理: {}, 跳过重复: {}, 处理失败: {}", processedCount, skippedCount, errorCount);
    }

    /**
     * 通过计算文件的首、中、尾部数据块的SHA-256哈希值，来快速生成文件的唯一标识.
     * 这种方法避免了读取整个大文件，显著提高了性能.
     *
     * @param path 目标文件路径
     * @return 计算出的部分哈希值
     * @throws IOException 如果读取文件失败
     */
    private String calculatePartialHash(Path path) throws IOException {
        final int CHUNK_SIZE = 1024 * 1024; // 1MB
        try (RandomAccessFile file = new RandomAccessFile(path.toFile(), "r")) {
            long fileSize = file.length();
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] buffer = new byte[CHUNK_SIZE];

            // 1. 读取头部
            if (fileSize > 0) {
                int bytesRead = file.read(buffer);
                if (bytesRead > 0) {
                    digest.update(buffer, 0, bytesRead);
                }
            }

            // 2. 读取中部
            if (fileSize > CHUNK_SIZE * 2) {
                file.seek(fileSize / 2 - CHUNK_SIZE / 2);
                int bytesRead = file.read(buffer);
                if (bytesRead > 0) {
                    digest.update(buffer, 0, bytesRead);
                }
            }

            // 3. 读取尾部
            if (fileSize > CHUNK_SIZE) {
                long seekPosition = Math.max(CHUNK_SIZE, fileSize - CHUNK_SIZE);
                file.seek(seekPosition);
                 int bytesRead = file.read(buffer);
                if (bytesRead > 0) {
                    digest.update(buffer, 0, bytesRead);
                }
            }

            byte[] hashBytes = digest.digest();
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                hexString.append(String.format("%02x", b));
            }
            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 算法不可用", e);
        }
    }


    /**
     * 检查内容哈希是否存在，如果存在则抛出异常.
     * @param hash 要检查的哈希值
     * @throws IllegalStateException 如果哈希已存在
     */
    private void checkDuplicateHash(String hash) {
        // 使用COUNT查询检查是否存在重复哈希，性能更好
        int count = this.baseMapper.countByContentHash(hash);
        if (count > 0) {
            throw new IllegalStateException("一个具有相同内容的文件已经存在，内容哈希: " + hash);
        }
    }

    /**
     * 解压文件并保存数据源记录的核心逻辑
     * @param archivePath 压缩文件路径
     * @param originalFilename 原始文件名
     * @param uploadType 上传类型
     * @param hash 文件的内容哈希
     * @return AnnotationImageSources 实体
     * @throws IOException IO异常
     */
    private AnnotationImageSources unzipAndSaveSourceRecord(Path archivePath, String originalFilename, String uploadType, String hash) throws IOException {
        // 1. 创建唯一的解压目录
        String uniqueDirName = System.currentTimeMillis() + "_" + originalFilename.replaceAll("\\..*$", "");
         Path targetExtractionPath = pathProperties.getExtractionBasePath().resolve(uniqueDirName);
        Files.createDirectories(targetExtractionPath);

        // 2. 解压文件
        try {
            unzipFile(archivePath, targetExtractionPath);
        } catch (Exception e) {
            // 如果解压失败，清理已创建的目录
            FileSystemUtils.deleteRecursively(targetExtractionPath);
            throw new IOException("解压文件失败: " + e.getMessage(), e);
        }

        // 3. 创建并保存数据源记录
        AnnotationImageSources newSource = new AnnotationImageSources();
        newSource.setSourceName(originalFilename);
        newSource.setUploadType(uploadType);
        newSource.setPath(targetExtractionPath.toString());
        newSource.setArchivePath(archivePath.toString());
        newSource.setContentHash(hash);
        newSource.setUploadedByUserId(SecurityUtils.getUserId());
//        this.save(newSource);
        annotationImageSourcesMapper.insertAnnotationImageSources(newSource);

        return newSource;
    }

    /**
     * 解压文件并自动处理其中的图片到指定分类
     * @param archivePath 压缩文件路径
     * @param originalFilename 原始文件名
     * @param uploadType 上传类型
     * @param hash 文件的内容哈希
     * @param categoryId 目标分类ID，如果为null则不自动处理图片
     * @return AnnotationImageSources 实体
     * @throws IOException IO异常
     */
    public AnnotationImageSources unzipAndProcessImages(Path archivePath, String originalFilename, String uploadType, String hash, Long categoryId) throws IOException {
        // 1. 解压并保存数据源记录
        AnnotationImageSources newSource = unzipAndSaveSourceRecord(archivePath, originalFilename, uploadType, hash);

        // 2. 如果指定了分类ID，自动处理图片
        if (categoryId != null) {
            try {
                log.info("开始自动处理数据源 {} 中的图片到分类 {}", newSource.getSourceId(), categoryId);
                annotationImagesService.processImagesFromSourceId(newSource.getSourceId(), categoryId);
                log.info("自动处理图片完成，数据源ID: {}, 分类ID: {}", newSource.getSourceId(), categoryId);
            } catch (Exception e) {
                log.error("自动处理图片失败，数据源ID: {}, 分类ID: {} - 错误: {}",
                    newSource.getSourceId(), categoryId, e.getMessage(), e);
                // 不抛出异常，允许数据源记录保存成功，图片处理可以后续手动执行
            }
        }

        return newSource;
    }

    /**
     * 【定时任务】定时扫描并导入文件夹中的压缩文件
     * 默认每10分钟执行一次，扫描指定目录下的压缩文件并自动导入
     * 需要确保在启动类上有 @EnableScheduling 注解
     *
     * 如果配置了 ylzx.annotation.auto-process-images=true，则会自动处理图片
     * 如果配置了 ylzx.annotation.default-category-id，则会将图片关联到指定分类
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void scheduledScanAndImport() {
        try {
            log.info("开始执行定时扫描任务...");
            if (pathProperties.isAutoProcessImages()) {
                log.info("启用自动图片处理，默认分类ID: {}", pathProperties.getDefaultCategoryId());
                scanAndImportSourcesWithImageProcessing(pathProperties.getDefaultCategoryId());
            } else {
                log.info("未启用自动图片处理，仅扫描和导入压缩文件");
                scanAndImportSources();
            }
            log.info("定时扫描任务执行完成");
        } catch (Exception e) {
            log.error("定时扫描任务执行失败", e);
        }
    }

    /**
     * 检查文件是否为支持的压缩文件格式
     * @param filename 文件名
     * @return 是否支持
     */
    private boolean isSupportedArchiveFile(String filename) {
        String lowerCaseName = filename.toLowerCase();
        return SUPPORTED_ARCHIVE_EXTENSIONS.stream()
                .anyMatch(lowerCaseName::endsWith);
    }

    /**
     * 根据压缩包类型解压文件
     * @param archivePath 压缩包路径
     * @param destinationDir 目标解压目录
     * @throws IOException IO异常
     */
    private void unzipFile(Path archivePath, Path destinationDir) throws IOException {
        String fileName = archivePath.toString().toLowerCase();
        if (fileName.endsWith(".zip")) {
//            ArchiveUtils.unzip(archivePath, destinationDir);
            ArchiveUtils.unzip(archivePath.toFile(), destinationDir.toFile());
        } else if (fileName.endsWith(".tar.gz")) {
            ArchiveUtils.untarGz(archivePath, destinationDir);
        } else {
            throw new UnsupportedOperationException("不支持的压缩文件类型: " + fileName);
        }
    }

    /**
     * 解压完成后自动处理图片（可选功能）
     * 如果需要在解压后立即处理图片，可以调用此方法
     * @param source 数据源记录
     * @param categoryId 分类ID（可选，如果为null则不创建分类关联）
     */
    private void processImagesAfterExtraction(AnnotationImageSources source, Long categoryId) {
        try {
            log.info("开始自动处理解压后的图片，sourceId: {}, categoryId: {}", source.getSourceId(), categoryId);
            if (categoryId != null) {
                // 创建分类与数据源的关联
                categoriesSourcesService.associateCategoryWithSource(categoryId, source.getSourceId(), null);
                log.info("已创建分类{}与数据源{}的关联", categoryId, source.getSourceId());

                // 处理图片并关联到指定分类
                annotationImagesService.processImagesFromSourceId(source.getSourceId(), categoryId);
                log.info("自动图片处理完成，sourceId: {}", source.getSourceId());
            } else {
                log.info("未指定categoryId，跳过图片处理，sourceId: {}", source.getSourceId());
            }
        } catch (Exception e) {
            log.error("自动处理图片失败，sourceId: {} - 异常: {}", source.getSourceId(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 扫描并导入源文件，同时自动处理图片
     * @param categoryId 分类ID，如果指定则自动处理图片并关联到该分类
     * @throws IOException IO异常
     */
    public void scanAndImportSourcesWithImageProcessing(Long categoryId) throws IOException {
        int processedCount = 0;
        int skippedCount = 0;
        int errorCount = 0;

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(pathProperties.getScanPath())) {
            for (Path filePath : stream) {
                if (Files.isRegularFile(filePath)) {
                    String filename = filePath.getFileName().toString();
                    if (isSupportedArchiveFile(filename)) {
                        String hash = null;
                        try {
                            log.info("正在处理文件: {}", filename);
                            // 1. 计算部分哈希并检查重复
                            hash = calculatePartialHash(filePath);
                            checkDuplicateHash(hash);

                            // 2. 解压并保存记录
                            AnnotationImageSources source = unzipAndSaveSourceRecord(filePath, filename, "scanned_import", hash);

                            // 2.1 自动处理解压后的图片
                            if (categoryId != null) {
                                processImagesAfterExtraction(source, categoryId);
                            }

                            // 3. 移动已处理的文件
                            Path destination = pathProperties.getProcessedPath().resolve(filename);
                            Files.move(filePath, destination, StandardCopyOption.REPLACE_EXISTING);
                            log.info("成功处理并移动文件: {}", filename);
                            processedCount++;
                        } catch (IllegalStateException e) {
                            log.warn("跳过文件（内容重复）: {} - 异常: {}", filename, e.getMessage());
                            // 对于重复文件，也移动到已处理目录，防止重复扫描
                            Path destination = pathProperties.getProcessedPath().resolve(filename);
                            Files.move(filePath, destination, StandardCopyOption.REPLACE_EXISTING);
                            skippedCount++;
                        } catch (Exception e) {
                            log.error("处理文件失败: {} - 异常: {}", filename, e.getMessage(), e);
                            // 移动到错误目录
                            try {
                                Path errorDestination = pathProperties.getErrorPath().resolve(filename);
                                Files.move(filePath, errorDestination, StandardCopyOption.REPLACE_EXISTING);
                                log.warn("已将失败文件移动到错误目录: {}", filename);
                            } catch (IOException moveException) {
                                log.error("移动失败文件到错误目录时出错: {} - 异常: {}", filename, moveException.getMessage(), moveException);
                            }
                            errorCount++;
                        }
                    }
                }
            }
        }

        log.info("扫描任务完成 - 成功处理: {}, 跳过重复: {}, 处理失败: {}", processedCount, skippedCount, errorCount);
    }

    /**
     * 【定时任务】清理过期的临时文件和空目录
     * 默认每天凌晨2点执行一次，清理超过7天的临时文件
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredFiles() {
        try {
            log.info("开始执行临时文件清理任务...");
            long cutoffTime = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000L); // 7天前

            // 清理解压目录中的过期文件夹
            cleanupExpiredDirectories(pathProperties.getExtractionBasePath(), cutoffTime);

            // 清理错误目录中的过期文件
            cleanupExpiredFiles(pathProperties.getErrorPath(), cutoffTime);

            log.info("临时文件清理任务执行完成");
        } catch (Exception e) {
            log.error("临时文件清理任务执行失败", e);
        }
    }

    /**
     * 清理指定目录中的过期目录
     */
    private void cleanupExpiredDirectories(Path baseDir, long cutoffTime) {
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(baseDir)) {
            for (Path dir : stream) {
                if (Files.isDirectory(dir)) {
                    try {
                        long lastModified = Files.getLastModifiedTime(dir).toMillis();
                        if (lastModified < cutoffTime) {
                            FileSystemUtils.deleteRecursively(dir);
                            log.info("已删除过期目录: {}", dir);
                        }
                    } catch (IOException e) {
                        log.warn("删除过期目录失败: {} - 异常: {}", dir, e.getMessage());
                    }
                }
            }
        } catch (IOException e) {
            log.error("清理过期目录时出错: {}", baseDir, e);
        }
    }

    /**
     * 清理指定目录中的过期文件
     */
    private void cleanupExpiredFiles(Path baseDir, long cutoffTime) {
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(baseDir)) {
            for (Path file : stream) {
                if (Files.isRegularFile(file)) {
                    try {
                        long lastModified = Files.getLastModifiedTime(file).toMillis();
                        if (lastModified < cutoffTime) {
                            Files.delete(file);
                            log.info("已删除过期文件: {}", file);
                        }
                    } catch (IOException e) {
                        log.warn("删除过期文件失败: {} - 异常: {}", file, e.getMessage());
                    }
                }
            }
        } catch (IOException e) {
            log.error("清理过期文件时出错: {}", baseDir, e);
        }
    }

    @Override
    public List<Map<String, Object>> getScanDirectoryFiles() {
        List<Map<String, Object>> files = new ArrayList<>();

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(pathProperties.getScanPath())) {
            for (Path filePath : stream) {
                if (Files.isRegularFile(filePath) && isSupportedArchiveFile(filePath.getFileName().toString())) {
                    Map<String, Object> fileInfo = new HashMap<>();
                    fileInfo.put("filename", filePath.getFileName().toString());
                    fileInfo.put("size", Files.size(filePath));
                    fileInfo.put("lastModified", Files.getLastModifiedTime(filePath).toMillis());
                    fileInfo.put("path", filePath.toString());
                    files.add(fileInfo);
                }
            }
        } catch (IOException e) {
            log.error("获取扫描目录文件列表失败", e);
        }

        return files;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnotationImageSources processScanFile(String filename, Long categoryId) throws IOException {
        Path scanPath = pathProperties.getScanPath();
        Path filePath = scanPath.resolve(filename);

        if (!Files.exists(filePath)) {
            throw new IllegalArgumentException("文件不存在: " + filename);
        }

        if (!isSupportedArchiveFile(filename)) {
            throw new IllegalArgumentException("不支持的文件格式: " + filename);
        }

        try {
            log.info("手动处理扫描文件: {}, 分类ID: {}", filename, categoryId);

            // 1. 计算部分哈希并检查重复
            String hash = calculatePartialHash(filePath);
            checkDuplicateHash(hash);

            // 2. 解压并保存记录
            AnnotationImageSources source = unzipAndSaveSourceRecord(filePath, filename, "scanned_import", hash);

            // 3. 如果指定了分类ID，创建关联并处理图片
            if (categoryId != null) {
                // 创建分类与数据源的关联
                categoriesSourcesService.associateCategoryWithSource(categoryId, source.getSourceId(), null);
                log.info("已创建分类{}与数据源{}的关联", categoryId, source.getSourceId());

                // 处理图片并关联到指定分类
                try {
                    annotationImagesService.processImagesFromSourceId(source.getSourceId(), categoryId);
                    log.info("已处理数据源{}的图片并关联到分类{}", source.getSourceId(), categoryId);
                } catch (Exception e) {
                    log.error("处理图片失败，数据源ID: {}, 分类ID: {} - 异常: {}", source.getSourceId(), categoryId, e.getMessage(), e);
                    // 不抛出异常，允许后续手动处理
                }
            } else {
                log.info("未指定分类ID，仅创建数据源记录，sourceId: {}", source.getSourceId());
            }

            // 4. 移动已处理的文件
            Path destination = pathProperties.getProcessedPath().resolve(filename);
            Files.move(filePath, destination, StandardCopyOption.REPLACE_EXISTING);
            log.info("成功处理并移动文件: {}", filename);

            return source;

        } catch (IllegalStateException e) {
            log.warn("跳过文件（内容重复）: {} - 异常: {}", filename, e.getMessage());
            // 对于重复文件，也移动到已处理目录，防止重复扫描
            Path destination = pathProperties.getProcessedPath().resolve(filename);
            Files.move(filePath, destination, StandardCopyOption.REPLACE_EXISTING);
            throw e; // 重新抛出，让调用者知道是重复文件
        } catch (Exception e) {
            log.error("处理文件失败: {} - 异常: {}", filename, e.getMessage(), e);
            // 移动到错误目录
            try {
                Path errorDestination = pathProperties.getErrorPath().resolve(filename);
                Files.move(filePath, errorDestination, StandardCopyOption.REPLACE_EXISTING);
                log.warn("已将失败文件移动到错误目录: {}", filename);
            } catch (IOException moveException) {
                log.error("移动失败文件到错误目录时出错: {} - 异常: {}", filename, moveException.getMessage(), moveException);
            }
            throw new IOException("处理文件失败: " + filename, e);
        }
    }
}
