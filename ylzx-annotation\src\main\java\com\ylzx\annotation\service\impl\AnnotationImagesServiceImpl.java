package com.ylzx.annotation.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylzx.annotation.config.AnnotationPathProperties;
import com.ylzx.annotation.domain.AnnotationCategories;
import com.ylzx.annotation.domain.AnnotationCategoriesImages;
import com.ylzx.annotation.domain.AnnotationImageSources;
import com.ylzx.annotation.domain.AnnotationImages;
import com.ylzx.annotation.mapper.AnnotationCategoriesImagesMapper;
import com.ylzx.annotation.mapper.AnnotationCategoriesMapper;
import com.ylzx.annotation.mapper.AnnotationImageSourcesMapper;
import com.ylzx.annotation.mapper.AnnotationImagesMapper;
import com.ylzx.annotation.service.AnnotationImagesService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * 标注图片Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class AnnotationImagesServiceImpl extends ServiceImpl<AnnotationImagesMapper, AnnotationImages> implements AnnotationImagesService {
    private static final Logger log = LoggerFactory.getLogger(AnnotationImagesServiceImpl.class);

    @Autowired
    private AnnotationImagesMapper annotationImagesMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AnnotationCategoriesImagesMapper categoriesImagesMapper;

    @Autowired
    private AnnotationCategoriesMapper categoriesMapper;

    @Autowired
    private ExecutorService virtualThreadExecutor;

    @Autowired
    private AnnotationPathProperties pathProperties;

    @Autowired
    private AnnotationImageSourcesMapper annotationImageSourcesMapper;


    @Autowired
    private AnnotationCategoriesMapper annotationCategoriesMapper;

    private static final List<String> SUPPORTED_IMAGE_EXTENSIONS = Arrays.asList(
            ".png", ".jpg", ".jpeg", ".gif", ".webp", ".heic", ".heif"
    );

    /**
     * 查询标注图片
     *
     * @param imageId 标注图片主键
     * @return 标注图片
     */
    @Override
    public AnnotationImages selectAnnotationImagesByImageId(Long imageId) {
        return annotationImagesMapper.selectAnnotationImagesByImageId(imageId);
    }

    /**
     * 查询标注图片列表
     *
     * @param annotationImages 标注图片
     * @return 标注图片集合
     */
    @Override
    public List<AnnotationImages> selectAnnotationImagesList(AnnotationImages annotationImages) {
        List<AnnotationImages> annotationImagesList = annotationImagesMapper.selectAnnotationImagesList(annotationImages);


//        List<AnnotationCategories> annotationCategoriesList = annotationCategoriesMapper.selectAnnotationCategoriesList(new AnnotationCategories());
        List<AnnotationImageSources> annotationImageSourcesList = annotationImageSourcesMapper.selectAnnotationImageSourcesList(AnnotationImageSources.builder().build());
        for (AnnotationImages images : annotationImagesList) {

            // 设置分类名称
//            for (AnnotationCategories category : annotationCategoriesList) {
//                if (images.getCategoryId().equals(category.getCategoryId())) {
//                    images.setCategoryName(category.getName());
//                    break;
//                }
//            }
            // 设置数据源名称
            for (AnnotationImageSources sources : annotationImageSourcesList) {
                if (images.getSourceId().equals(sources.getSourceId())) {
                    images.setSourceName(sources.getSourceName());
                    break;
                }
            }
        }

        return annotationImagesList;
    }

    /**
     * 新增标注图片
     *
     * @param annotationImages 标注图片
     * @return 结果
     */
    @Override
    public int insertAnnotationImages(AnnotationImages annotationImages) {
        return annotationImagesMapper.insertAnnotationImages(annotationImages);
    }

    /**
     * 修改标注图片
     *
     * @param annotationImages 标注图片
     * @return 结果
     */
    @Override
    public int updateAnnotationImages(AnnotationImages annotationImages) {
        return annotationImagesMapper.updateAnnotationImages(annotationImages);
    }

    /**
     * 批量删除标注图片
     *
     * @param imageIds 需要删除的标注图片主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationImagesByImageIds(Long[] imageIds) {
        return annotationImagesMapper.deleteAnnotationImagesByImageIds(imageIds);
    }

    /**
     * 删除标注图片信息
     *
     * @param imageId 标注图片主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationImagesByImageId(Long imageId) {
        return annotationImagesMapper.deleteAnnotationImagesByImageId(imageId);
    }

    @Override
    public void processImagesFromSourceId(Long sourceId, Long categoryId) {

//        AnnotationImageSources source = annotationImageSourcesMapper.selectById(sourceId);

        List<AnnotationImageSources> annotationImageSources = annotationImageSourcesMapper.selectAnnotationImageSourcesList(AnnotationImageSources.builder().sourceId(sourceId).build());

        AnnotationImageSources source = annotationImageSources.get(0);
        if (source == null || source.getFileId() == null || source.getFileId().isEmpty()) {
            log.error("数据源未找到或其文件ID为空，sourceId: {}", sourceId);
            throw new IllegalArgumentException("数据源未找到或其文件ID为空，sourceId: " + sourceId);
        }
        // TODO: 需要实现基于MinIO的图片处理逻辑
        log.warn("processImagesFromSourceId方法需要实现MinIO版本，sourceId: {}", sourceId);
        throw new UnsupportedOperationException("该方法需要实现MinIO版本");
    }

    private void processImagesInDirectory(Path directoryPath, Long sourceId, Long categoryId) {
        // 解析路径：如果是相对路径，则基于 annotation_data 根目录进行解析
//        Path resolvedPath = resolveDirectoryPath(directoryPath);
        Path resolvedPath = directoryPath;

        try {
            log.info("开始处理目录 {} 中的图片", resolvedPath);
            List<AnnotationImages> imagesToStore;
            try (Stream<Path> pathStream = Files.walk(resolvedPath)) {
                List<CompletableFuture<AnnotationImages>> futures = pathStream
                        .filter(Files::isRegularFile)
                        .filter(this::isSupportedImage)
                        .map(imagePath -> CompletableFuture.supplyAsync(() -> {
                            try {
                                AnnotationImages image = new AnnotationImages();
                                image.setSourceId(sourceId);
                                image.setCategoryId(categoryId);
                                // TODO: 需要将文件上传到MinIO并设置fileId
                                image.setFileId("temp_file_id"); // 临时设置，需要实现MinIO上传
                                image.setOriginalFilename(imagePath.getFileName().toString());
                                image.setFileSizeBytes(Files.size(imagePath));
                                image.setUploadedAt(LocalDateTime.now());

                                // Read image dimensions
                                try (InputStream is = Files.newInputStream(imagePath)) {
                                    BufferedImage bimg = ImageIO.read(is);
                                    if (bimg != null) {
                                        image.setWidth((long) bimg.getWidth());
                                        image.setHeight((long) bimg.getHeight());
                                    } else {
                                        log.warn("无法读取图片尺寸: {}", imagePath);
                                    }
                                } catch (IOException e) {
                                    log.error("读取图片元数据时出错: {}", imagePath, e);
                                    return null;
                                }

                                // Calculate MD5 hash
                                try (InputStream is = Files.newInputStream(imagePath)) {
                                    MessageDigest md = MessageDigest.getInstance("MD5");
                                    byte[] buffer = new byte[8192];
                                    int bytesRead;
                                    while ((bytesRead = is.read(buffer)) != -1) {
                                        md.update(buffer, 0, bytesRead);
                                    }
                                    byte[] digest = md.digest();
                                    StringBuilder sb = new StringBuilder();
                                    for (byte b : digest) {
                                        sb.append(String.format("%02x", b));
                                    }
                                    String md5Hash = sb.toString();
                                    image.setMd5Hash(md5Hash);

                                    // 检查MD5是否重复
                                    int count = annotationImagesMapper.countByMd5Hash(md5Hash);
                                    if (count > 0) {
                                        log.debug("跳过重复图片（MD5已存在）: {} - MD5: {}", imagePath, md5Hash);
                                        return null;
                                    }
                                } catch (NoSuchAlgorithmException | IOException e) {
                                    log.error("计算MD5时出错: {}", imagePath, e);
                                    return null;
                                }

                                return image;
                            } catch (IOException e) {
                                log.error("处理文件时出错: {}", imagePath, e);
                                return null;
                            }
                        }, virtualThreadExecutor))
                        .toList();

                imagesToStore = futures.stream()
                        .map(CompletableFuture::join)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }

            // 分批保存图片信息，每200条保存一次
            if (!imagesToStore.isEmpty()) {
                final int batchSize = 500;
                log.info("正在向数据库分批保存 {} 张图片，每批 {} 条。", imagesToStore.size(), batchSize);

                // 分批保存图片信息
                for (int i = 0; i < imagesToStore.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, imagesToStore.size());
                    List<AnnotationImages> batch = imagesToStore.subList(i, endIndex);
                    log.info("正在保存第 {} 批图片，数量: {} (总进度: {}/{})",
                            (i / batchSize + 1), batch.size(), endIndex, imagesToStore.size());
                    annotationImagesMapper.insertBatch(batch);
                    System.out.println();
                }
            } else {
                log.warn("在目录 {} 中未找到支持的图片。", resolvedPath);
            }


            // 再次验证分类是否存在，防止处理过程中分类被删除
            AnnotationCategories category = categoriesMapper.selectAnnotationCategoriesByCategoryId(categoryId);
            if (category == null) {
                log.warn("跳过关联关系保存，分类不存在，categoryId: {}", categoryId);
            } else {
//                    List<AnnotationImages> annotationImages = annotationImagesMapper.selectAnnotationImagesList(AnnotationImages.builder().build());
//                    imagesToStore.stream().forEach(image -> {
//                        AnnotationImages annotationImages1 = annotationImages.stream().filter(img -> img.getMd5Hash().equals(image.getMd5Hash())).collect(Collectors.toList()).get(0);
//                        if (annotationImages1 != null) {
//                            image.setImageId(annotationImages1.getImageId());
//                        }
//                    });

//                    List<AnnotationCategoriesImages> categoriesToStore = imagesToStore.stream()
//                            .map(image -> {
//                                AnnotationCategoriesImages categoryImage = new AnnotationCategoriesImages();
//                                categoryImage.setCategoryId(categoryId);
//                                categoryImage.setImageId(image.getImageId());
//                                return categoryImage;
//                            })
//                            .collect(Collectors.toList());
                imagesToStore = annotationImagesMapper.selectAnnotationImagesList(AnnotationImages.builder().sourceId(sourceId).build());
                List<AnnotationCategoriesImages> annotationCategoriesImagesList = categoriesImagesMapper.selectAnnotationCategoriesImagesList(AnnotationCategoriesImages.builder().build());
                // 保存图片与分类的关联关系
                List<AnnotationCategoriesImages> categoriesToStore = imagesToStore.stream()
                        .map(image -> {
                            AnnotationCategoriesImages categoryImage = new AnnotationCategoriesImages();
                            categoryImage.setCategoryId(categoryId);
                            categoryImage.setImageId(image.getImageId());
                            return categoryImage;
                        }).filter(item -> {
//                            List<AnnotationCategoriesImages> annotationCategoriesImages = categoriesImagesMapper.selectAnnotationCategoriesImagesList(item);
//                            return  annotationCategoriesImages.size() == 0;
                            for (AnnotationCategoriesImages person : annotationCategoriesImagesList) {
                                if (item.getImageId().equals(person.getImageId()) && item.getCategoryId().equals(person.getCategoryId())) { // 调用getName()获取姓名
                                    return false;
                                }
                            }
                            return true;
                        })
                        .collect(Collectors.toList());


                int batchSize = 500;
                log.info("正在分批保存 {} 条分类-图片关联记录，每批 {} 条。", categoriesToStore.size(), batchSize);
                // 分批保存关联关系
                for (int i = 0; i < categoriesToStore.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, categoriesToStore.size());
                    List<AnnotationCategoriesImages> batch = categoriesToStore.subList(i, endIndex);
                    log.info("正在保存第 {} 批关联记录，数量: {} (总进度: {}/{})",
                            (i / batchSize + 1), batch.size(), endIndex, categoriesToStore.size());
                    // 使用 mapper 直接批量插入而不是通过 service
                    categoriesImagesMapper.insertBatch(batch);
                }
            }


        } catch (IOException e) {
            log.error("处理目录失败: {}", resolvedPath, e);
            throw new RuntimeException("处理目录失败: " + resolvedPath, e);
        }
        // 清理临时目录的逻辑已移至 AnnotationImageSourcesService, 因为现在处理的是永久解压目录
    }

    private boolean isSupportedImage(Path path) {
        String fileName = path.toString().toLowerCase();
        return SUPPORTED_IMAGE_EXTENSIONS.stream().anyMatch(fileName::endsWith);
    }

    /**
     * 解析目录路径：如果是相对路径，则基于 annotation_data 根目录进行解析
     *
     * @param directoryPath 输入的目录路径
     * @return 解析后的绝对路径
     */
    private Path resolveDirectoryPath(Path directoryPath) {
        // 如果路径已经是绝对路径，直接返回
        if (directoryPath.isAbsolute()) {
            return directoryPath;
        }

        // 如果是相对路径，基于 annotation_data 根目录进行解析
        Path basePath = Paths.get(pathProperties.getBasePath());
        Path resolvedPath = basePath.resolve(directoryPath);

        log.info("路径解析: {} -> {}", directoryPath, resolvedPath);
        return resolvedPath;
    }

    // ylzx-file服务的基础URL
    private static final String FILE_SERVICE_URL = "http://localhost:8083/file/api/file";

    @Override
    public String getImagePresignedUrl(Long imageId) {
        try {
            AnnotationImages image = annotationImagesMapper.selectAnnotationImagesByImageId(imageId);
            if (image == null || image.getFileId() == null) {
                return null;
            }

            ResponseEntity<String> response = restTemplate.getForEntity(
                FILE_SERVICE_URL + "/presigned-url/" + image.getFileId(), String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                if (jsonNode.get("code").asInt() == 200) {
                    return jsonNode.get("data").asText();
                }
            }
            return null;
        } catch (Exception e) {
            log.error("获取图片预签名URL失败: {}", imageId, e);
            return null;
        }
    }

    @Override
    public Map<String, String> getImagePresignedUrls(List<Long> imageIds) {
        Map<String, String> result = new HashMap<>();

        // 批量查询图片信息
        List<AnnotationImages> images = annotationImagesMapper.selectBatchIds(imageIds);

        // 提取fileId列表
        List<String> fileIds = images.stream()
            .filter(img -> img.getFileId() != null)
            .map(AnnotationImages::getFileId)
            .collect(Collectors.toList());

        if (fileIds.isEmpty()) {
            return result;
        }

        // 批量获取预签名URL
        for (String fileId : fileIds) {
            try {
                ResponseEntity<String> response = restTemplate.getForEntity(
                    FILE_SERVICE_URL + "/presigned-url/" + fileId, String.class);

                if (response.getStatusCode() == HttpStatus.OK) {
                    JsonNode jsonNode = objectMapper.readTree(response.getBody());
                    if (jsonNode.get("code").asInt() == 200) {
                        String url = jsonNode.get("data").asText();
                        // 找到对应的imageId
                        images.stream()
                            .filter(img -> fileId.equals(img.getFileId()))
                            .forEach(img -> result.put(img.getImageId().toString(), url));
                    }
                }
            } catch (Exception e) {
                log.error("获取预签名URL失败: {}", fileId, e);
            }
        }

        return result;
    }

    @Override
    public List<AnnotationImages> addPresignedUrlsToImages(List<AnnotationImages> images) {
        if (images == null || images.isEmpty()) {
            return images;
        }

        // 提取imageId列表
        List<Long> imageIds = images.stream()
            .map(AnnotationImages::getImageId)
            .collect(Collectors.toList());

        // 批量获取预签名URL
        Map<String, String> urlMap = getImagePresignedUrls(imageIds);

        // 为每个图片设置预签名URL
        images.forEach(image -> {
            String url = urlMap.get(image.getImageId().toString());
            if (url != null) {
                // 这里可以设置一个临时字段来存储URL，或者通过其他方式传递给前端
                // 由于AnnotationImages实体类可能没有url字段，可以考虑扩展DTO
                log.debug("图片 {} 的预签名URL: {}", image.getImageId(), url);
            }
        });

        return images;
    }
}
