-- 迁移到MinIO的数据库表结构修改脚本

-- 1. 修改 annotation_image_sources 表
-- 添加新字段
ALTER TABLE annotation_image_sources 
ADD COLUMN file_id VARCHAR(32) COMMENT '文件ID（MinIO中的文件标识）';

ALTER TABLE annotation_image_sources 
ADD COLUMN archive_file_id VARCHAR(32) COMMENT '压缩包文件ID（MinIO中的压缩包文件标识）';

-- 创建索引
CREATE INDEX idx_annotation_image_sources_file_id ON annotation_image_sources(file_id);
CREATE INDEX idx_annotation_image_sources_archive_file_id ON annotation_image_sources(archive_file_id);

-- 2. 修改 annotation_images 表
-- 添加新字段
ALTER TABLE annotation_images 
ADD COLUMN file_id VARCHAR(32) COMMENT '文件ID（MinIO中的文件标识）';

-- 创建索引
CREATE INDEX idx_annotation_images_file_id ON annotation_images(file_id);

-- 3. 数据迁移脚本（可选，如果需要保留现有数据）
-- 注意：这个脚本需要根据实际情况调整，因为需要将现有的文件上传到MinIO并获取file_id

-- 示例迁移脚本（需要手动执行）：
-- UPDATE annotation_image_sources SET file_id = 'new_file_id_from_minio' WHERE source_id = 1;
-- UPDATE annotation_images SET file_id = 'new_file_id_from_minio' WHERE image_id = 1;

-- 4. 在完成数据迁移后，可以考虑删除旧字段（谨慎操作）
-- ALTER TABLE annotation_image_sources DROP COLUMN path;
-- ALTER TABLE annotation_image_sources DROP COLUMN archive_path;
-- ALTER TABLE annotation_images DROP COLUMN storage_path;

-- 5. 添加注释
COMMENT ON COLUMN annotation_image_sources.file_id IS '文件ID，对应ylzx-file服务中的file_info表的id字段';
COMMENT ON COLUMN annotation_image_sources.archive_file_id IS '压缩包文件ID，对应ylzx-file服务中的file_info表的id字段';
COMMENT ON COLUMN annotation_images.file_id IS '文件ID，对应ylzx-file服务中的file_info表的id字段';
